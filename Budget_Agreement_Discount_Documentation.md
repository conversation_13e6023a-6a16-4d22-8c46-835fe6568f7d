# Budget Agreement Discount System - Complete Documentation

## Table of Contents
1. [Project Overview](#project-overview)
2. [Architecture & Structure](#architecture--structure)
3. [Discount Model Types](#discount-model-types)
4. [Form Fields & Validations](#form-fields--validations)
5. [API Endpoints & Data Flow](#api-endpoints--data-flow)
6. [Conditional Field Logic](#conditional-field-logic)
7. [Form Components Structure](#form-components-structure)
8. [Usage Examples](#usage-examples)

## Project Overview

The Budget Agreement Discount System is a comprehensive React-based application for managing discount agreements within budget frameworks. The system supports complex discount models with hierarchical structures (discounts and sub-discounts) and provides extensive configuration options.

### Key Features
- **14 Different Discount Model Types** with unique configurations
- **Hierarchical Discount Structure** (Parent Discounts → Sub-Discounts)
- **Dynamic Form Validation** based on model type and context
- **Conditional Field Rendering** based on business rules
- **Complex Parameter Management** with multiple calculation types
- **Traffic Segmentation** and operator-specific configurations
- **Multi-currency Support** with settlement methods
- **Qualifying Rules** for discount eligibility

### Main Components
- **Budget Management**: Top-level budget container
- **Agreement Management**: Individual agreements within budgets
- **Discount Management**: Discount creation, editing, and deletion
- **Sub-Discount Management**: Nested discount structures
- **Form Validation**: Comprehensive validation system
- **API Integration**: RESTful API communication

## Architecture & Structure

### Directory Structure
```
src/pages/
├── BudgetDetails/
│   └── BudgetItems/
│       └── Agreements/
├── AgreementDetails/
│   └── AgreementItems/
│       └── Discounts/
│           ├── shared/
│           │   ├── DiscountForm/
│           │   │   ├── DiscountTraffic/
│           │   │   ├── DiscountParameters/
│           │   │   ├── DiscountQualifying/
│           │   │   ├── DiscountModelType/
│           │   │   └── AdditionalAttributes/
│           │   └── DiscountChilds/
│           ├── DiscountCreation/
│           ├── DiscountEditing/
│           ├── DiscountDeleting/
│           ├── SubDiscountCreation/
│           └── SubDiscountEditing/
```

### Core Technologies
- **React 18** with Hooks
- **Redux** for state management
- **Formik** for form handling
- **Yup** for validation
- **Material-UI** for components
- **Axios** for API communication

### Data Flow
1. **Budget** → Contains multiple **Agreements**
2. **Agreement** → Contains multiple **Discounts**
3. **Discount** → Can contain multiple **Sub-Discounts**
4. Each level has specific validation rules and field configurations

## Discount Model Types

The system supports 14 distinct discount model types, each with unique configurations:

### 1. Single Rate Effective (SRE)
- **Value**: `SINGLE_RATE_EFFECTIVE`
- **Description**: Simple single-rate discount model
- **Settlement Method**: Credit Note EoA
- **Parameters**: Single rate with volume bounds
- **Use Case**: Basic discount scenarios

### 2. Stepped/Tiered
- **Value**: `STEPPED_TIERED`
- **Description**: Multi-tier discount structure
- **Parameters**: Multiple tiers with different rates
- **Bounds**: Volume-based tiers
- **Use Case**: Volume-based progressive discounts

### 3. Send or Pay Traffic + SRE
- **Value**: `SEND_OR_PAY_TRAFFIC_SINGLE_RATE_EFFECTIVE`
- **Description**: Traffic-based single rate model
- **Service Types**: Specific service type combinations
- **Parameters**: Traffic volume calculations
- **Use Case**: Traffic-specific discount scenarios

### 4. Send or Pay Traffic + Stepped/Tiered
- **Value**: `SEND_OR_PAY_TRAFFIC_STEPPED_TIERED`
- **Description**: Traffic-based tiered model
- **Parameters**: Multiple tiers for traffic volumes
- **Calculation**: Send or Pay Traffic basis
- **Use Case**: Complex traffic-based discounts

### 5. Send or Pay Financial
- **Value**: `SEND_OR_PAY_FINANCIAL`
- **Description**: Financial commitment model
- **Features**: Commitment distribution parameters
- **Bounds**: Financial commitment thresholds
- **Use Case**: Financial guarantee scenarios

### 6. Balanced/Unbalanced (SRE)
- **Value**: `BALANCED_UNBALANCED_SINGLE_RATE_EFFECTIVE`
- **Description**: Balance-aware single rate model
- **Balancing Options**: Balanced, Unbalanced, No Balancing
- **Parameters**: Balance-specific calculations
- **Use Case**: Traffic balance considerations

### 7. Balanced/Unbalanced (Tiered)
- **Value**: `BALANCED_UNBALANCED_STEPPED_TIERED`
- **Description**: Balance-aware tiered model
- **Features**: Multi-tier with balancing
- **Parameters**: Balance and tier combinations
- **Use Case**: Complex balance-based discounts

### 8. Back to First
- **Value**: `BACK_TO_FIRST`
- **Description**: Retroactive discount application
- **Calculation**: Back-to-first tier pricing
- **Parameters**: Retroactive rate adjustments
- **Use Case**: Retroactive discount scenarios

### 9. Per Month Per IMSI
- **Value**: `PER_MONTH_PER_IMSI`
- **Description**: IMSI-based monthly calculations
- **Bounds**: Unique IMSI count per month
- **Service Types**: Access fee specific
- **Use Case**: IMSI-based pricing models

### 10. Per Month Per IMSI - Above Threshold
- **Value**: `PER_MONTH_PER_IMSI_ABOVE_THRESHOLD`
- **Description**: Threshold-based IMSI model
- **Features**: Financial threshold support
- **Additional Fields**: Above threshold rates
- **Use Case**: Threshold-triggered IMSI pricing

### 11. Per Month Per IMSI - Back to First
- **Value**: `PER_MONTH_PER_IMSI_BACK_TO_FIRST`
- **Description**: Retroactive IMSI-based model
- **Calculation**: Back-to-first with IMSI counts
- **Parameters**: IMSI-specific retroactive pricing
- **Use Case**: Complex IMSI retroactive scenarios

### 12. Per Month Per IMSI - Stepped/Tiered
- **Value**: `PER_MONTH_PER_IMSI_STEPPED_TIERED`
- **Description**: Tiered IMSI-based model
- **Features**: Multiple IMSI count tiers
- **Parameters**: Tiered IMSI calculations
- **Use Case**: Progressive IMSI-based pricing

### 13. Per Month Per IMSI with Incremental Charging
- **Value**: `PER_MONTH_PER_IMSI_WITH_INCREMENTAL_CHARGING`
- **Description**: Incremental IMSI charging model
- **Features**: Access fee and incremental rates
- **Parameters**: Volume included in access fee
- **Use Case**: Incremental IMSI-based charging

### 14. All You Can Eat
- **Value**: `ALL_YOU_CAN_EAT`
- **Description**: Unlimited usage model
- **Settlement**: Credit Note EoA only
- **Bounds**: Financial commitment only
- **Use Case**: Unlimited usage scenarios

## Form Fields & Validations

### Core Traffic Fields

#### Home Operators
- **Field**: `home_operators`
- **Type**: Array of operator objects
- **Validation**: Minimum 1 operator required
- **Format**: `{id: number, pmn_code: string}`

#### Partner Operators
- **Field**: `partner_operators`
- **Type**: Array of operator objects
- **Validation**: Minimum 1 operator required
- **Format**: `{id: number, pmn_code: string}`

#### Validity Period
- **Valid From**: `start_date` (Date, required)
- **Valid To**: `end_date` (String, required)
- **Validation**: Date format validation

#### Direction
- **Field**: `direction`
- **Type**: Object with value/title
- **Options**: Inbound, Outbound, Both
- **Validation**: Required selection

#### Service Types
- **Field**: `service_types`
- **Type**: Array of service type objects
- **Validation**: Minimum 1 service type required
- **Options**: Voice, SMS, Data, etc.

#### Settlement & Currency
- **Settlement Method**: `settlement_method` (Required)
- **Currency**: `currency_code` (Required)
- **Tax Type**: `tax_type` (NET/GROSS, Required)
- **Volume Type**: `volume_type` (ACTUAL/BILLED, Required)

### Additional Attributes (Conditional)

#### Call Destinations
- **Field**: `call_destinations`
- **Type**: Array of destination objects
- **Condition**: Available for all discount types
- **Mutual Exclusion**: Cannot be used with Called Countries

#### Called Countries
- **Field**: `called_countries`
- **Type**: Array of country codes
- **Condition**: Available for all discount types
- **Mutual Exclusion**: Cannot be used with Call Destinations

#### Traffic Segments
- **Field**: `traffic_segments`
- **Type**: Array of traffic segment objects
- **Condition**: Available for all discount types
- **Dependency**: Requires home operators selection

#### Rate Above Commitment
- **Field**: `above_commitment_rate`
- **Type**: Number (nullable)
- **Condition**: Only available for sub-discounts
- **Validation**: Rate format (6 integer, 10 decimal digits)

#### Inbound Market Share
- **Field**: `inbound_market_share`
- **Type**: Number (nullable)
- **Condition**: Available based on model type configuration
- **Validation**: Bounds format (15 integer, 5 decimal digits)

#### Financial Threshold
- **Field**: `financial_threshold`
- **Type**: Number (nullable)
- **Condition**: Available based on model type configuration
- **Validation**: Bounds format validation

#### Above Threshold Rate
- **Field**: `above_financial_threshold_rate`
- **Type**: Number (nullable)
- **Condition**: Only available for sub-discounts
- **Validation**: Rate format validation

### Parameter Fields

#### Calculation Type
- **Field**: `calculation_type`
- **Type**: Object with value/title
- **Options**: Varies by model type
- **Validation**: Required, model-specific options

#### Discount Basis
- **Field**: `basis`
- **Type**: Object with value/title
- **Options**: Value, Percentage (model-dependent)
- **Validation**: Model-specific availability

#### Basis Value
- **Field**: `basis_value`
- **Type**: Number (nullable)
- **Condition**: Enabled/disabled based on model type
- **Validation**: Rate format when enabled

#### Bound Type
- **Field**: `bound_type`
- **Type**: Object with value/title
- **Options**: Volume, Financial Commitment, IMSI Count, Market Share
- **Validation**: Model-specific options

#### Lower/Upper Bounds
- **Fields**: `lower_bound`, `upper_bound`
- **Type**: Number (nullable)
- **Condition**: Enabled/disabled based on model type
- **Validation**: Bounds format validation

#### Balancing
- **Field**: `balancing`
- **Type**: Object with value/title
- **Options**: Balanced, Unbalanced, No Balancing
- **Condition**: Available for balanced/unbalanced models

#### Access Fee Rate
- **Field**: `access_fee_rate`
- **Type**: Number (nullable)
- **Condition**: Available for IMSI-based models
- **Validation**: Rate format validation

#### Incremental Rate
- **Field**: `incremental_rate`
- **Type**: Number (nullable)
- **Condition**: Available for incremental charging models
- **Validation**: Rate format validation

### Qualifying Rule Fields

#### Qualifying Direction
- **Field**: `direction` (within qualifying_rule)
- **Type**: Object with value/title
- **Options**: Same as main direction options
- **Validation**: Optional

#### Qualifying Service Types
- **Field**: `service_types` (within qualifying_rule)
- **Type**: Array of service type objects
- **Options**: Same as main service types
- **Validation**: Optional

#### Qualifying Basis
- **Field**: `basis` (within qualifying_rule)
- **Type**: Object with value/title
- **Options**: Volume, Market Share %, IMSI Count, Average Usage
- **Validation**: Optional

#### Qualifying Bounds
- **Fields**: `lower_bound`, `upper_bound` (within qualifying_rule)
- **Type**: Number (nullable)
- **Validation**: Bounds format, lower_bound required if basis selected

### Commitment Distribution Parameters

#### Distribution Operators
- **Home Operators**: Array of operator objects (min 1 required)
- **Partner Operators**: Array of operator objects (min 1 required)
- **Charge**: Number with bounds validation

#### Validation Rules
- **Minimum Quantity**: 2 distribution parameters required
- **Operator Validation**: Each parameter must have operators
- **Charge Validation**: Bounds format validation

### Validation Configurations

#### Rate Validation
- **Integer Part**: Maximum 6 digits
- **Decimal Part**: Maximum 10 digits
- **Format**: Positive numbers only

#### Bounds Validation
- **Integer Part**: Maximum 15 digits
- **Decimal Part**: Maximum 5 digits
- **Format**: Positive numbers only

#### Array Validations
- **Operators**: Minimum 1 item required
- **Service Types**: Minimum 1 item required
- **Parameters**: Model-specific min/max quantities

#### Conditional Validations
- **Model Type Dependent**: Field availability changes
- **Sub-Discount Context**: Different field sets available
- **Mutual Exclusions**: Some fields cannot be used together

## API Endpoints & Data Flow

### Base URLs and Structure
- **Base API Path**: `/budget-agreements/{agreementId}/discounts`
- **Sub-Discount Path**: `/budget-agreements/{agreementId}/discounts/{parentDiscountId}/sub-discounts`
- **HTTP Client**: Axios with query string serialization
- **Parameter Serialization**: `qs.stringify(params, { arrayFormat: 'repeat' })`

### Core API Endpoints

#### 1. Get Discounts
- **Method**: `GET`
- **URL**: `/budget-agreements/{budgetAgreementId}/discounts`
- **Purpose**: Retrieve all discounts for an agreement
- **Parameters**:
  - Query parameters for filtering
  - Signal for request cancellation
- **Response**: Array of discount objects
- **Service**: `getDiscounts(signal, budgetAgreementId, data)`

#### 2. Create Discount
- **Method**: `POST`
- **URL**: `/budget-agreements/{id}/discounts`
- **Purpose**: Create a new discount
- **Request Body**: Transformed form data
- **Response**: Created discount object
- **Service**: `createDiscount(id, data)`
- **Action**: `createDiscountAction(id, requestData)`

#### 3. Edit Discount
- **Method**: `PATCH`
- **URL**: `/budget-agreements/{id}/discounts/{discountId}`
- **Purpose**: Update existing discount
- **Request Body**: Transformed form data
- **Response**: Updated discount object
- **Service**: `editDiscount(id, discountId, data)`
- **Action**: `editDiscountAction(id, discountId, requestData)`

#### 4. Delete Discount
- **Method**: `DELETE`
- **URL**: `/budget-agreements/{agreementId}/discounts/{discountId}`
- **Purpose**: Delete a discount
- **Response**: Deletion confirmation
- **Service**: `deleteDiscount(agreementId, discountId)`
- **Action**: `deleteDiscountAction(agreementId, discountId)`

#### 5. Create Sub-Discount
- **Method**: `POST`
- **URL**: `/budget-agreements/{agreementId}/discounts/{parentDiscountId}/sub-discounts`
- **Purpose**: Create a sub-discount under a parent discount
- **Request Body**: Transformed form data
- **Response**: Created sub-discount object
- **Service**: `createSubDiscount(agreementId, parentDiscountId, data)`
- **Action**: `createSubDiscountAction(agreementId, parentDiscountId, requestData)`

#### 6. Edit Sub-Discount
- **Method**: `PATCH`
- **URL**: `/budget-agreements/{agreementId}/discounts/{parentDiscountId}/sub-discounts/{subDiscountId}`
- **Purpose**: Update existing sub-discount
- **Request Body**: Transformed form data
- **Response**: Updated sub-discount object
- **Service**: `editSubDiscount(agreementId, parentDiscountId, subDiscountId, data)`
- **Action**: `editSubDiscountAction(agreementId, parentDiscountId, subDiscountId, requestData)`

### Agreement Management APIs

#### 1. Get Agreements
- **Method**: `GET`
- **URL**: `/budgets/{budgetId}/agreements/`
- **Purpose**: Retrieve all agreements for a budget
- **Parameters**: Budget ID and filter parameters
- **Service**: `getAgreements(signal, data)`

#### 2. Create Agreement
- **Method**: `POST`
- **URL**: `/budgets/{budgetId}/agreements`
- **Purpose**: Create a new agreement
- **Service**: `createAgreement(budgetId, params)`

#### 3. Activate All Agreements
- **Method**: `POST`
- **URL**: `/budgets/{budgetId}/agreements/activated/bulk`
- **Purpose**: Bulk activate agreements
- **Service**: `activateAllAgreements(budgetId)`

#### 4. Deactivate All Agreements
- **Method**: `POST`
- **URL**: `/budgets/{budgetId}/agreements/deactivated/bulk`
- **Purpose**: Bulk deactivate agreements
- **Service**: `deactivateAllAgreements(budgetId)`

### Data Transformation Utilities

#### Form to API Data Conversion
**Function**: `getConvertedForRequestFormData(formData)`

**Transformations**:
```javascript
// Model Type
[discountModelTypeField]: formData[discountModelTypeField].value

// Operators (Extract IDs)
[discountFields.homeOperators]: getIds(formData[discountFields.homeOperators])
[discountFields.partnerOperators]: getIds(formData[discountFields.partnerOperators])

// Dates
[discountFields.validFrom]: formData[discountFields.validFrom]
[discountFields.validTo]: formData[discountFields.validTo]

// Single Value Objects
[discountFields.discountDirection]: formData[discountFields.discountDirection].value
[discountFields.discountSettlementMethod]: formData[discountFields.discountSettlementMethod].value
[discountFields.discountCurrency]: formData[discountFields.discountCurrency].code

// Arrays (Extract Values)
[discountFields.serviceTypes]: getArrayByKey(formData[discountFields.serviceTypes], 'value')
[discountFields.callDestinations]: getArrayByKey(formData[discountFields.callDestinations], 'value')
[discountFields.trafficSegments]: getArrayByKey(formData[discountFields.trafficSegments], 'id')

// Numeric Fields (Convert and Validate)
[discountFields.rateAboveCommitment]: getConvertedFormattedNumber(value)
[discountFields.inboundMarketShare]: getConvertedFormattedNumber(value)
[discountFields.financialThreshold]: getConvertedFormattedNumber(value)
[discountFields.aboveThresholdRate]: getConvertedFormattedNumber(value)

// Parameters Array
parameters: formData.parameters.map((row) => ({
  [discountParametersFields.calculationType]: row[field]?.value,
  [discountParametersFields.discountBasis]: row[field]?.value,
  [discountParametersFields.discountBasisValue]: getConvertedFormattedNumber(row[field]),
  [discountParametersFields.boundType]: row[field]?.value,
  [discountParametersFields.lowerBound]: getConvertedFormattedNumber(row[field]),
  [discountParametersFields.upperBound]: getConvertedFormattedNumber(row[field]),
  [discountParametersFields.balancing]: row[field]?.value,
  [discountParametersFields.accessFeeRate]: getConvertedFormattedNumber(row[field]),
  [discountParametersFields.incrementalRate]: getConvertedFormattedNumber(row[field])
}))

// Qualifying Rule (Conditional)
[discountQualifyingRule]: isDiscountQualifyingRequiredFieldsEmpty(formData[discountQualifyingRule])
  ? null
  : {
      [discountQualifyingFields.direction]: formData[rule][field]?.value,
      [discountQualifyingFields.serviceTypes]: getArrayByKey(formData[rule][field], 'value'),
      [discountQualifyingFields.basis]: formData[rule][field]?.value,
      [discountQualifyingFields.lowerBound]: getConvertedFormattedNumber(formData[rule][field]),
      [discountQualifyingFields.upperBound]: getConvertedFormattedNumber(formData[rule][field])
    }

// Commitment Distribution Parameters (Conditional)
[commitmentDistributionParameters]: formData[commitmentDistributionParameters]?.map((row) => ({
  [commitmentDistributionParametersFields.homeOperators]: getIds(row[field]),
  [commitmentDistributionParametersFields.partnerOperators]: getIds(row[field]),
  [commitmentDistributionParametersFields.charge]: getConvertedFormattedNumber(row[field])
})) || null
```

#### API to Form Data Conversion
**Function**: `useInitialDiscountFormValues(discountDataFromRequest)`

**Transformations**:
```javascript
// Single Value Conversions
getConvertedSingleData({
  field: discountModelTypeField,
  config: availableDiscountModelTypes,
  requestData: discountDataFromRequest
})

// Multiple Value Conversions
getConvertedMultipleData({
  field: discountFields.serviceTypes,
  config: serviceTypeConfig,
  requestData: discountDataFromRequest
})

// Currency Conversion
[discountFields.discountCurrency]: currencies.find(
  ({ code }) => code === discountDataFromRequest[discountFields.discountCurrency]
)

// Numeric Field Truncation
[discountParametersFields.lowerBound]: truncateZero(parameter[field])
[discountParametersFields.upperBound]: truncateZero(parameter[field])

// Qualifying Rule Conversion
getConvertedDiscountQualifyingRuleData(): isNull(discountDataFromRequest[discountQualifyingRule])
  ? defaultDiscountQualifyingValues
  : convertedQualifyingRuleData

// Parameters Array Conversion
parameters: discountDataFromRequest.parameters?.map((parameter) => ({
  ...parameter,
  [discountParametersFields.calculationType]: getConvertedSingleData({...}),
  [discountParametersFields.discountBasis]: getConvertedSingleData({...}),
  [discountParametersFields.boundType]: getConvertedSingleData({...}),
  [discountParametersFields.balancing]: getConvertedSingleData({...}),
  // Numeric fields with truncation
  [discountParametersFields.lowerBound]: truncateZero(parameter[field]),
  [discountParametersFields.upperBound]: truncateZero(parameter[field])
}))
```

### Error Handling

#### API Error Responses
```javascript
// Standard Error Handling
try {
  await dispatch(createDiscountAction(agreementId, formData));
  dispatch(showSuccessToastr(toastrMessages.successCreation));
} catch (e) {
  const errorToastrText = e.response.data?.detail
    ? e.response.data?.detail
    : toastrMessages.errorCreation;
  dispatch(showErrorToastr(errorToastrText));
}

// Sub-Discount Error Handling
export const getErrorMessage = (e) => {
  let errorMessage = toastrMessages.errorCreation;

  if (e.response.data?.detail) {
    errorMessage = e.response.data?.detail;
  } else if (e.response.data?.length) {
    [errorMessage] = e.response.data;
  }

  return errorMessage;
};
```

#### Redux Action Patterns
```javascript
// Standard Action Pattern
const createDiscountAction = (id, requestData) => async (dispatch) => {
  try {
    dispatch(createDiscountRequest());
    const { data } = await createDiscount(id, requestData);
    dispatch(createDiscountSuccess(data));
    return data;
  } catch (error) {
    dispatch(createDiscountFailure(error));
    throw error;
  }
};
```

### Data Flow Sequence

#### 1. Discount Creation Flow
1. User fills form → Form validation (Yup schema)
2. Form submission → `getConvertedForRequestFormData(formData)`
3. API call → `createDiscountAction(agreementId, transformedData)`
4. Success → Refresh discount list + Success toast
5. Error → Display error toast with API message

#### 2. Discount Editing Flow
1. Load existing data → `useInitialDiscountFormValues(discountData)`
2. Form initialization → Convert API data to form format
3. User edits → Real-time validation
4. Form submission → Transform and submit
5. Success → Refresh + Success notification

#### 3. Sub-Discount Management Flow
1. Parent discount selection → Enable sub-discount creation
2. Sub-discount form → Limited model types available
3. Additional attributes → Different field sets for sub-discounts
4. API submission → Nested endpoint structure
5. Hierarchy update → Refresh parent discount data

### Request/Response Examples

#### Create Discount Request
```json
{
  "model_type": "SINGLE_RATE_EFFECTIVE",
  "home_operators": [36488],
  "partner_operators": [36489],
  "start_date": "2025-01-01",
  "end_date": "2025-12-31",
  "direction": "INBOUND",
  "service_types": ["VOICE", "SMS"],
  "settlement_method": "CREDIT_NOTE_EOA",
  "currency_code": "USD",
  "tax_type": "NET",
  "volume_type": "ACTUAL",
  "parameters": [{
    "calculation_type": "SINGLE_RATE_EFFECTIVE",
    "basis": "VALUE",
    "basis_value": 0.05,
    "bound_type": "VOLUME",
    "lower_bound": 1000,
    "upper_bound": null
  }],
  "qualifying_rule": null,
  "commitment_distribution_parameters": null,
  "sub_discounts": null
}
```

#### Get Discounts Response
```json
[
  {
    "id": 9819,
    "agreement_id": 6145,
    "model_type": "SINGLE_RATE_EFFECTIVE",
    "home_operators": [{"id": 36488, "pmn_code": "UKRAS"}],
    "partner_operators": [{"id": 36489, "pmn_code": "USATT"}],
    "start_date": "2025-01-01",
    "end_date": "2025-12-31",
    "direction": "INBOUND",
    "service_types": ["VOICE", "SMS"],
    "settlement_method": "CREDIT_NOTE_EOA",
    "currency_code": "USD",
    "tax_type": "NET",
    "volume_type": "ACTUAL",
    "parameters": [...],
    "qualifying_rule": null,
    "commitment_distribution_parameters": null,
    "sub_discounts": [...]
  }
]
```

## Conditional Field Logic

### Model Type-Based Field Configuration

Each discount model type has a specific configuration that determines:
- Available settlement methods
- Service type combinations
- Parameter configurations
- Additional field availability
- Validation rules

#### Configuration Structure
```javascript
const configForModelType = {
  availableValues: {
    [discountFields.discountSettlementMethod]: [...], // Available settlement methods
    [discountFields.serviceTypes]: {...}, // Service type configuration
    parameters: [...], // Parameter configurations
    [discountFields.inboundMarketShare]: boolean, // Field availability
    [discountFields.financialThreshold]: boolean // Field availability
  },
  maxParametersQuantity: number, // Maximum parameter rows
  minParametersQuantity: number, // Minimum parameter rows
  additionalFields: {...} // Additional field configurations
};
```

### Model-Specific Configurations

#### Single Rate Effective (SRE)
```javascript
availableValues: {
  [discountFields.discountSettlementMethod]: [discountSettlementMethodOptions.creditNoteEoA],
  [discountFields.serviceTypes]: {
    getAvailableConfig: getAllServiceTypesCombinations,
    enableSelectAll: true
  },
  parameters: [{
    [discountParametersFields.calculationType]: [calculationTypesOptions.singleRateEffective],
    [discountParametersFields.discountBasis]: [discountBasisOptions.value],
    [discountParametersFields.discountBasisValue]: { isDisabled: false },
    [discountParametersFields.boundType]: [boundTypeOptions.volume],
    [discountParametersFields.lowerBound]: { isDisabled: false },
    [discountParametersFields.upperBound]: { isDisabled: false }
  }]
},
maxParametersQuantity: 1,
minParametersQuantity: 1
```

#### All You Can Eat
```javascript
availableValues: {
  [discountFields.discountSettlementMethod]: [discountSettlementMethodOptions.creditNoteEoA],
  [discountFields.serviceTypes]: {
    getAvailableConfig: getAllServiceTypesCombinations,
    enableSelectAll: true
  },
  parameters: [{
    [discountParametersFields.calculationType]: [calculationTypesOptions.allYouCanEat],
    [discountParametersFields.discountBasis]: [], // Empty - not available
    [discountParametersFields.discountBasisValue]: { isDisabled: true },
    [discountParametersFields.boundType]: [boundTypeOptions.financial],
    [discountParametersFields.lowerBound]: { isDisabled: false },
    [discountParametersFields.upperBound]: { isDisabled: true }
  }]
},
maxParametersQuantity: 1,
minParametersQuantity: 1
```

#### Send or Pay Financial
```javascript
availableValues: {
  [discountFields.discountSettlementMethod]: [discountSettlementMethodOptions.creditNoteEoA],
  [discountFields.serviceTypes]: {
    getAvailableConfig: getAllServiceTypesCombinations,
    enableSelectAll: true
  },
  parameters: [{
    [discountParametersFields.calculationType]: [calculationTypesOptions.sendOrPayFinancial],
    [discountParametersFields.discountBasis]: [],
    [discountParametersFields.discountBasisValue]: { isDisabled: true },
    [discountParametersFields.boundType]: [boundTypeOptions.financial],
    [discountParametersFields.lowerBound]: { isDisabled: false },
    [discountParametersFields.upperBound]: { isDisabled: true }
  }]
},
maxParametersQuantity: 1,
minParametersQuantity: 1,
additionalFields: {
  [commitmentDistributionParameters]: true // Enables commitment distribution
}
```

#### Per Month Per IMSI - Above Threshold
```javascript
availableValues: {
  [discountFields.discountSettlementMethod]: [discountSettlementMethodOptions.creditNoteEoA],
  [discountFields.serviceTypes]: {
    getAvailableConfig: getAccessFeeServiceType,
    enableSelectAll: false
  },
  parameters: [{
    [discountParametersFields.calculationType]: [calculationTypesOptions.perMonthPerIMSIAboveThreshold],
    [discountParametersFields.discountBasis]: [discountBasisOptions.value],
    [discountParametersFields.discountBasisValue]: { isDisabled: false },
    [discountParametersFields.boundType]: [boundTypeOptions.uniqueIMSICountPerMonth],
    [discountParametersFields.lowerBound]: { isDisabled: false },
    [discountParametersFields.upperBound]: { isDisabled: true },
    [discountParametersFields.accessFeeRate]: { isDisabled: false }
  }]
},
maxParametersQuantity: 1,
minParametersQuantity: 1,
additionalFields: {
  [discountFields.financialThreshold]: true // Enables financial threshold field
}
```

### Sub-Discount Restrictions

#### Available Model Types for Sub-Discounts
```javascript
export const availableSubDiscountModelTypes = [
  modelTypesOptions.singleRateEffective,
  modelTypesOptions.steppedTiered,
  modelTypesOptions.backToFirst,
  modelTypesOptions.perMonthPerIMSI,
  modelTypesOptions.perMonthPerIMSIAboveThreshold,
  modelTypesOptions.perMonthPerIMSIBackToFirst,
  modelTypesOptions.perMonthPerIMSISteppedTiered,
  modelTypesOptions.perMonthPerIMSIWithIncrementalCharging
];
```

#### Sub-Discount Field Differences
- **Rate Above Commitment**: Only available for sub-discounts
- **Above Threshold Rate**: Only available for sub-discounts
- **Inbound Market Share**: NOT available for sub-discounts
- **Financial Threshold**: NOT available for sub-discounts
- **Commitment Distribution**: NOT available for sub-discounts

### Additional Attributes Conditional Logic

#### Configuration Function
```javascript
export const getDefaultAdditionalAttributesConfig = ({
  formik,
  isSubDiscount,
  isInboundMarketShare,
  isFinancialThreshold
}) => {
  const defaultConfig = {
    callDestinations: { /* Always available */ },
    trafficSegments: { /* Always available */ }
  };

  const conditionalConfig = {
    // Only for sub-discounts
    ...(isSubDiscount && {
      rateAboveCommitment: { /* Sub-discount only */ },
      aboveThresholdRate: { /* Sub-discount only */ }
    }),

    // Only for main discounts with specific model types
    ...(!isSubDiscount && isInboundMarketShare && {
      inboundMarketShare: { /* Main discount only */ }
    }),

    ...(!isSubDiscount && isFinancialThreshold && {
      financialThreshold: { /* Main discount only */ }
    })
  };

  return { ...defaultConfig, ...conditionalConfig };
};
```

#### Field Availability Matrix

| Field | Main Discount | Sub-Discount | Condition |
|-------|---------------|--------------|-----------|
| Call Destinations | ✅ | ✅ | Always available |
| Traffic Segments | ✅ | ✅ | Always available |
| Rate Above Commitment | ❌ | ✅ | Sub-discount only |
| Above Threshold Rate | ❌ | ✅ | Sub-discount only |
| Inbound Market Share | ✅* | ❌ | Model type dependent |
| Financial Threshold | ✅* | ❌ | Model type dependent |

*Depends on model type configuration

### Service Type Combinations

#### Service Type Configuration Types
```javascript
// All combinations available with select all option
{
  getAvailableConfig: getAllServiceTypesCombinations,
  enableSelectAll: true
}

// Default combinations without select all
{
  getAvailableConfig: getDefaultServiceTypesCombinationBasedOnValue,
  enableSelectAll: false
}

// Access fee specific (IMSI models)
{
  getAvailableConfig: getAccessFeeServiceType,
  enableSelectAll: false
}
```

#### Service Type Mutual Exclusions
- **Call Destinations** and **Called Countries** are mutually exclusive
- When one is selected, the other becomes disabled
- Clearing one enables the other

### Parameter Field Dependencies

#### Basis Value Dependency
```javascript
[discountParametersFields.discountBasisValue]: {
  isDisabled: !discountBasis || discountBasis.length === 0
}
```

#### Bound Dependencies
```javascript
// Lower bound typically required when bound type is selected
[discountParametersFields.lowerBound]: {
  isDisabled: !boundType
}

// Upper bound availability varies by model
[discountParametersFields.upperBound]: {
  isDisabled: modelType !== 'STEPPED_TIERED'
}
```

#### Access Fee and Incremental Rate Dependencies
```javascript
// Access fee rate - only for IMSI-based models
[discountParametersFields.accessFeeRate]: {
  isDisabled: !isIMSIBasedModel(calculationType)
}

// Incremental rate - only for incremental charging models
[discountParametersFields.incrementalRate]: {
  isDisabled: calculationType !== 'PER_MONTH_PER_IMSI_WITH_INCREMENTAL_CHARGING'
}
```

### Qualifying Rule Conditional Logic

#### Qualifying Rule States
```javascript
export const discountQualifyingStates = {
  empty: 'EMPTY',           // No fields filled
  partiallyFilled: 'PARTIALLY_FILLED', // Some fields filled
  filled: 'FILLED'          // All required fields filled
};
```

#### Required Fields for Qualifying Rule
```javascript
export const discountQualifyingRequiredFields = [
  discountQualifyingFields.direction,
  discountQualifyingFields.serviceTypes,
  discountQualifyingFields.basis,
  discountQualifyingFields.lowerBound
];
```

#### Qualifying Rule Validation
- If any required field is filled, all required fields become mandatory
- Upper bound is optional
- Empty qualifying rule is converted to null in API request

### Commitment Distribution Conditional Logic

#### Availability Condition
```javascript
// Only available for specific model types
additionalFields: {
  [commitmentDistributionParameters]: true
}
```

#### Minimum Requirements
```javascript
export const minCommitmentDistributionQuantity = 2;
```

#### Validation Rules
- Minimum 2 distribution parameters required
- Each parameter must have home and partner operators
- Charge field is required for each parameter
- Cannot be used with sub-discounts

### Dynamic Form Behavior

#### Model Type Change Impact
When model type changes:
1. **Parameters reset** to default configuration for new model
2. **Additional fields** availability recalculated
3. **Service types** configuration updated
4. **Settlement methods** filtered to available options
5. **Validation schema** updated for new constraints

#### Form State Management
```javascript
const useDiscountFormConfigByModelType = (modelType) => {
  const discountFormConfig = discountFormConfigByModelType[modelType?.value];

  return {
    discountFormConfig,
    availableValues: discountFormConfig?.availableValues || {},
    maxParametersQuantity: discountFormConfig?.maxParametersQuantity || 1,
    minParametersQuantity: discountFormConfig?.minParametersQuantity || 1,
    additionalFields: discountFormConfig?.additionalFields || {}
  };
};
```

#### Field Reset Logic
```javascript
// When additional attribute is disabled
resetFieldsValues: () => formik.setValues((values) => ({
  ...values,
  [discountFields.fieldName]: defaultValue
}))
```

### Validation Dependencies

#### Cross-Field Validation
- **Operators**: Home and partner operators must be different
- **Date Range**: Valid from must be before valid to
- **Bounds**: Lower bound must be less than upper bound (when both present)
- **Service Types**: Must have at least one service type selected

#### Model-Specific Validation
- **All You Can Eat**: Only financial bounds allowed
- **IMSI Models**: Unique IMSI count bounds required
- **Balanced Models**: Balancing field becomes required
- **Tiered Models**: Multiple parameters with different bounds required

### Real-Time Field Updates

#### Dependent Field Updates
```javascript
// When home operators change, traffic segments are filtered
useEffect(() => {
  if (homeOperators.length === 0) {
    setTrafficSegments([]);
  }
}, [homeOperators]);

// When model type changes, reset dependent fields
useEffect(() => {
  resetParametersToDefault();
  updateAdditionalFieldsAvailability();
}, [modelType]);
```

#### Form Synchronization
- **Parameter quantity** adjusts based on model type limits
- **Field availability** updates immediately on model type change
- **Validation errors** clear when fields become unavailable
- **Default values** populate when fields become available

## Form Components Structure

### Component Hierarchy

#### Main Form Component
```
DiscountForm (Main Container)
├── DiscountModelType (Model Selection)
├── PerfectScrollbar (Scroll Container)
│   └── div.discount-form__accordions
│       ├── DiscountQualifying (Qualifying Rules)
│       ├── AdditionalAttributes (Optional Fields)
│       ├── DiscountTraffic (Core Traffic Fields)
│       ├── DiscountParameters (Calculation Parameters)
│       └── CommitmentDistribution (Financial Distribution)
└── DiscountFormErrorInfo (Error Display)
```

#### Modal Containers
```
DiscountCreation
├── DiscountCreationModalContent
│   ├── DiscountChilds (Sub-discount Management)
│   └── DiscountForm

SubDiscountCreation
├── SubDiscountCreationModalContent
│   └── DiscountForm (with isSubDiscount=true)

DiscountEditing
├── DiscountEditingModalContent
│   └── DiscountForm (with initial values)

SubDiscountEditing
├── SubDiscountEditingModalContent
│   └── DiscountForm (with isSubDiscount=true, initial values)
```

### Core Form Components

#### 1. DiscountForm (Main Container)
**Path**: `src/pages/AgreementDetails/AgreementItems/Discounts/shared/DiscountForm/DiscountForm.jsx`

**Props**:
```javascript
{
  initialDiscountFormValues: Object,           // Pre-populated form data
  closeModal: Function,                        // Modal close handler
  submitForm: Function,                        // Form submission handler
  initialAdditionalFormFieldsConfiguration: Object, // Additional fields config
  isSubDiscount: Boolean                       // Sub-discount context flag
}
```

**Key Features**:
- Formik integration with Yup validation
- Model type state management
- Dynamic form configuration based on model type
- Additional fields configuration management
- Form submission with data transformation

**State Management**:
```javascript
const [modelType, setModelType] = useState(initialModelType);
const [additionalFormFieldsConfiguration, setAdditionalFormFieldsConfiguration] = useState(initialConfig);
```

#### 2. DiscountModelType (Model Selection)
**Path**: `src/pages/AgreementDetails/AgreementItems/Discounts/shared/DiscountForm/DiscountModelType/DiscountModelType.jsx`

**Props**:
```javascript
{
  modelType: Object,        // Current model type selection
  setModelType: Function,   // Model type change handler
  isSubDiscount: Boolean    // Determines available model types
}
```

**Features**:
- Model type dropdown with filtered options
- Information tooltip about model type changes
- Different model types for main discounts vs sub-discounts
- Reset warning when changing model types

#### 3. DiscountTraffic (Core Fields)
**Path**: `src/pages/AgreementDetails/AgreementItems/Discounts/shared/DiscountForm/DiscountTraffic/DiscountTraffic.jsx`

**Components**:
- **Operators Selection**: Home and partner operators
- **Date Range**: Validity period picker
- **Direction**: Traffic direction selection
- **Service Types**: Multi-select with model-specific options
- **Settlement & Currency**: Settlement method and currency selection
- **Tax & Volume Types**: Radio button selections

**Conditional Fields**:
- **Call Destinations**: Autocomplete with mutual exclusion
- **Called Countries**: Multi-select with mutual exclusion
- **IMSI Count Type**: For IMSI-based models
- **Traffic Segments**: Dependent on home operators

#### 4. DiscountParameters (Calculation Setup)
**Path**: `src/pages/AgreementDetails/AgreementItems/Discounts/shared/DiscountForm/DiscountParameters/DiscountParameters.jsx`

**Features**:
- Dynamic parameter rows based on model type
- Add/remove parameter controls
- Model-specific field configurations
- Parameter validation and formatting

**Sub-Components**:
- **DiscountParameter**: Individual parameter row
- **DiscountParametersControls**: Add/remove buttons
- **DiscountParametersHead**: Column headers

**Fields per Parameter**:
- Calculation Type (dropdown)
- Discount Basis (dropdown, conditional)
- Basis Value (number input, conditional)
- Bound Type (dropdown)
- Lower/Upper Bounds (number inputs, conditional)
- Balancing (dropdown, conditional)
- Access Fee Rate (number input, conditional)
- Incremental Rate (number input, conditional)

#### 5. DiscountQualifying (Qualifying Rules)
**Path**: `src/pages/AgreementDetails/AgreementItems/Discounts/shared/DiscountForm/DiscountQualifying/DiscountQualifying.jsx`

**Features**:
- Accordion-style collapsible section
- Status indicator (Empty/Partially Filled/Filled)
- Optional qualifying criteria setup

**Fields**:
- Direction (dropdown)
- Service Types (multi-select)
- Basis (dropdown)
- Lower/Upper Bounds (number inputs)

#### 6. AdditionalAttributes (Optional Fields)
**Path**: `src/pages/AgreementDetails/AgreementItems/Discounts/shared/DiscountForm/AdditionalAttributes/AdditionalAttributes.jsx`

**Features**:
- Checkbox-based field activation
- Dynamic field rendering based on selection
- Context-aware field availability
- Field reset functionality when disabled

**Available Attributes**:
- Call Destinations
- Traffic Segments
- Rate Above Commitment (sub-discounts only)
- Inbound Market Share (main discounts only)
- Financial Threshold (main discounts only)
- Above Threshold Rate (sub-discounts only)

#### 7. CommitmentDistribution (Financial Distribution)
**Path**: `src/pages/AgreementDetails/AgreementItems/Discounts/shared/DiscountForm/DiscountParameters/CommitmentDistribution/`

**Features**:
- Only available for specific model types
- Minimum 2 distribution parameters required
- Add/remove distribution controls
- Operator and charge configuration per distribution

**Sub-Components**:
- **CommitmentDistributionParameters**: Main container
- **CommitmentDistributionParameter**: Individual distribution row
- **CommitmentDistributionParametersControls**: Add/remove controls
- **CommitmentDistributionParametersHead**: Table headers

### Shared Components

#### 1. DiscountChilds (Sub-Discount Management)
**Path**: `src/pages/AgreementDetails/AgreementItems/Discounts/shared/DiscountChilds/DiscountChilds.jsx`

**Purpose**: Manage sub-discounts for parent discounts
**Features**:
- Sub-discount creation modal integration
- Sub-discount editing capabilities
- Sub-discount deletion
- Parent-child relationship management

#### 2. DiscountModalTitle
**Path**: `src/pages/AgreementDetails/AgreementItems/Discounts/shared/DiscountModalTitle/DiscountModalTitle.jsx`

**Purpose**: Standardized modal titles for discount operations
**Variants**: Creation, Editing, Sub-discount creation, Sub-discount editing

#### 3. DiscountFormErrorInfo
**Path**: `src/pages/AgreementDetails/AgreementItems/Discounts/shared/DiscountForm/DiscountFormErrorInfo/DiscountFormErrorInfo.jsx`

**Purpose**: Display form validation errors
**Features**: Formik error integration, user-friendly error messages

### Utility Components

#### 1. Autocomplete Components
- **OperatorsAutocomplete**: Operator selection with search
- **CurrenciesAutocomplete**: Currency selection with search
- **TrafficSegmentsAutocomplete**: Traffic segment selection
- **CalledCountriesSelect**: Country selection component

#### 2. Input Components
- **FormattedNumber**: Number input with formatting
- **RangePicker**: Date range selection
- **RadioButtons**: Radio button groups
- **Autocomplete**: Generic autocomplete component

### Form State Management

#### Formik Configuration
```javascript
const formik = useFormik({
  initialValues: initialDiscountFormValues,
  validationSchema: DiscountFormSchema,
  onSubmit: async (values) => {
    await submitForm(values);
  },
  enableReinitialize: true
});
```

#### State Synchronization
```javascript
// Model type changes trigger form reconfiguration
useEffect(() => {
  if (modelType?.value !== formik.values[discountModelTypeField]?.value) {
    // Reset parameters to default for new model type
    const defaultParameters = getDefaultParametersForModelType(modelType);
    formik.setFieldValue('parameters', defaultParameters);

    // Update additional fields availability
    setAdditionalFormFieldsConfiguration(getUpdatedConfig(modelType));
  }
}, [modelType]);
```

#### Dynamic Validation
```javascript
// Validation schema adapts to current form state
const DiscountFormSchema = Yup.object().shape({
  // Static validations
  [discountFields.homeOperators]: Yup.array().min(1),

  // Conditional validations based on model type
  [discountFields.inboundMarketShare]: Yup.number()
    .nullable(true)
    .when(discountModelTypeField, {
      is: (modelType) => supportsInboundMarketShare(modelType),
      then: Yup.number().test(boundsValidationConfig)
    }),

  // Parameter validations
  parameters: Yup.array().of(
    Yup.object().shape({
      [discountParametersFields.calculationType]: Yup.object().required(),
      // ... other parameter validations
    })
  )
});
```

### Component Communication Patterns

#### Props Drilling Pattern
```javascript
// Parent passes configuration down to children
<DiscountForm
  isSubDiscount={isSubDiscount}
  initialAdditionalFormFieldsConfiguration={config}
>
  <AdditionalAttributes
    isSubDiscount={isSubDiscount}
    isInboundMarketShare={isInboundMarketShare}
    isFinancialThreshold={isFinancialThreshold}
  />
</DiscountForm>
```

#### Callback Pattern
```javascript
// Child components communicate changes back to parent
<DiscountParameters
  formik={formik}
  discountFormConfig={discountFormConfig}
  onParameterChange={(newParameters) => {
    formik.setFieldValue('parameters', newParameters);
  }}
/>
```

#### Context Pattern
```javascript
// Agreement context provides shared data
const { agreementId } = useAgreementDetailsContext();
```

### Reusable Utilities

#### 1. Form Data Transformation
```javascript
// Convert form data to API format
export const getConvertedForRequestFormData = (formData) => { /* ... */ };

// Convert API data to form format
export const useInitialDiscountFormValues = (apiData) => { /* ... */ };
```

#### 2. Validation Utilities
```javascript
// Number format validation
export const ratesValidationConfig = {
  message: 'up to 6 digits integer part; up to 10 digits decimal part.',
  test: (value) => validateNumberFormat(value, 6, 10)
};

export const boundsValidationConfig = {
  message: 'up to 15 digits integer part; up to 5 digits decimal part.',
  test: (value) => validateNumberFormat(value, 15, 5)
};
```

#### 3. Configuration Utilities
```javascript
// Get model-specific configuration
export const useDiscountFormConfigByModelType = (modelType) => {
  return discountFormConfigByModelType[modelType?.value] || {};
};

// Get additional attributes configuration
export const getDefaultAdditionalAttributesConfig = (options) => { /* ... */ };
```

#### 4. Data Processing Utilities
```javascript
// Extract IDs from object arrays
export const getIds = (array) => array.map(item => item.id);

// Extract values by key from object arrays
export const getArrayByKey = (array, key) => array.map(item => item[key]);

// Convert and validate numbers
export const getConvertedFormattedNumber = (value) =>
  value === '' || value === null ? value : Number(value);
```

### Component Testing Patterns

#### Mock Data Structure
```javascript
const mockFormData = {
  [discountModelTypeField]: { value: 'SINGLE_RATE_EFFECTIVE' },
  [discountFields.homeOperators]: [{ id: 1, pmn_code: 'TEST' }],
  [discountFields.partnerOperators]: [{ id: 2, pmn_code: 'PARTNER' }],
  // ... other fields
};
```

#### Component Testing
```javascript
describe('DiscountForm', () => {
  const defaultProps = {
    closeModal: jest.fn(),
    submitForm: jest.fn(),
    initialDiscountFormValues: mockFormData,
    initialAdditionalFormFieldsConfiguration: mockConfig
  };

  test('renders form with initial values', () => {
    render(<DiscountForm {...defaultProps} />);
    // Test form rendering
  });

  test('handles model type changes', () => {
    // Test model type change behavior
  });
});
```

### Performance Considerations

#### Memoization
```javascript
// Memoize expensive calculations
const discountFormConfig = useMemo(() =>
  getDiscountFormConfigByModelType(modelType), [modelType]
);

// Memoize callback functions
const handleParameterChange = useCallback((newParameters) => {
  formik.setFieldValue('parameters', newParameters);
}, [formik]);
```

#### Lazy Loading
```javascript
// Lazy load heavy components
const CommitmentDistribution = lazy(() =>
  import('./CommitmentDistribution/CommitmentDistribution')
);
```

#### Conditional Rendering Optimization
```javascript
// Only render components when needed
{discountFormConfig.additionalFields?.[commitmentDistributionParameters] && (
  <CommitmentDistribution formik={formik} />
)}
```

## Usage Examples

### 1. Creating a Simple Single Rate Effective Discount

#### Step-by-Step Process
1. **Select Model Type**: Choose "Single Rate Effective (SRE)"
2. **Configure Traffic**:
   - Home Operators: Select at least one operator
   - Partner Operators: Select at least one operator
   - Validity Period: Set start and end dates
   - Direction: Choose Inbound/Outbound/Both
   - Service Types: Select Voice, SMS, Data, etc.
   - Settlement Method: Credit Note EoA (auto-selected)
   - Currency: Select currency
   - Tax Type: NET or GROSS
   - Volume Type: ACTUAL or BILLED

3. **Configure Parameters**:
   - Calculation Type: Single Rate Effective (auto-selected)
   - Discount Basis: Value
   - Basis Value: Enter discount rate (e.g., 0.05)
   - Bound Type: Volume
   - Lower Bound: Enter minimum volume (e.g., 1000)
   - Upper Bound: Optional maximum volume

4. **Optional Qualifying Rules**:
   - Direction: Optional traffic direction filter
   - Service Types: Optional service type filter
   - Basis: Volume/Market Share/IMSI Count/Average Usage
   - Lower Bound: Minimum threshold for qualification
   - Upper Bound: Optional maximum threshold

#### Example Configuration
```json
{
  "model_type": "SINGLE_RATE_EFFECTIVE",
  "home_operators": [{"id": 1, "pmn_code": "UKRAS"}],
  "partner_operators": [{"id": 2, "pmn_code": "USATT"}],
  "start_date": "2025-01-01",
  "end_date": "2025-12-31",
  "direction": "INBOUND",
  "service_types": ["VOICE", "SMS"],
  "settlement_method": "CREDIT_NOTE_EOA",
  "currency_code": "USD",
  "tax_type": "NET",
  "volume_type": "ACTUAL",
  "parameters": [{
    "calculation_type": "SINGLE_RATE_EFFECTIVE",
    "basis": "VALUE",
    "basis_value": 0.05,
    "bound_type": "VOLUME",
    "lower_bound": 1000,
    "upper_bound": null
  }]
}
```

### 2. Creating a Complex Send or Pay Financial Discount

#### Configuration Steps
1. **Model Type**: "Send or Pay Financial"
2. **Traffic Configuration**: Standard traffic fields
3. **Parameters**:
   - Calculation Type: Send or Pay Financial (auto-selected)
   - Bound Type: Financial Commitment (auto-selected)
   - Lower Bound: Minimum financial commitment
   - Basis and Basis Value: Disabled for this model

4. **Commitment Distribution** (Required):
   - Add minimum 2 distribution parameters
   - Each parameter needs home operators, partner operators, and charge percentage
   - Total charges should typically sum to 100%

#### Example Configuration
```json
{
  "model_type": "SEND_OR_PAY_FINANCIAL",
  "parameters": [{
    "calculation_type": "SEND_OR_PAY_FINANCIAL",
    "bound_type": "FINANCIAL_COMMITMENT",
    "lower_bound": 100000
  }],
  "commitment_distribution_parameters": [
    {
      "home_operators": [1],
      "partner_operators": [2],
      "charge": 60
    },
    {
      "home_operators": [3],
      "partner_operators": [4],
      "charge": 40
    }
  ]
}
```

### 3. Creating a Per Month Per IMSI Above Threshold Discount

#### Configuration Steps
1. **Model Type**: "Per Month Per IMSI - Above Threshold"
2. **Service Types**: Only Access Fee service types available
3. **Parameters**:
   - Calculation Type: Per Month Per IMSI - Above Threshold
   - Discount Basis: Value
   - Basis Value: Rate per IMSI
   - Bound Type: Unique IMSI Count per Month
   - Lower Bound: Minimum IMSI count threshold
   - Access Fee Rate: Monthly access fee per IMSI

4. **Additional Attributes**:
   - Financial Threshold: Available for this model type
   - Above Threshold Rate: Available for sub-discounts

#### Example Configuration
```json
{
  "model_type": "PER_MONTH_PER_IMSI_ABOVE_THRESHOLD",
  "service_types": ["ACCESS_FEE"],
  "parameters": [{
    "calculation_type": "PER_MONTH_PER_IMSI_ABOVE_THRESHOLD",
    "basis": "VALUE",
    "basis_value": 2.50,
    "bound_type": "UNIQUE_IMSI_COUNT_PER_MONTH",
    "lower_bound": 1000,
    "access_fee_rate": 5.00
  }],
  "financial_threshold": 50000
}
```

### 4. Creating Sub-Discounts

#### Process
1. **Create Parent Discount**: Use any main discount model type
2. **Add Sub-Discount**: Click "Add Sub-Discount" in parent discount
3. **Limited Model Types**: Only specific model types available for sub-discounts
4. **Different Fields**: Rate above commitment and above threshold rate available
5. **Restrictions**: No inbound market share, financial threshold, or commitment distribution

#### Sub-Discount Example
```json
{
  "model_type": "SINGLE_RATE_EFFECTIVE",
  "parameters": [{
    "calculation_type": "SINGLE_RATE_EFFECTIVE",
    "basis": "VALUE",
    "basis_value": 0.03,
    "bound_type": "VOLUME",
    "lower_bound": 5000
  }],
  "above_commitment_rate": 0.02
}
```

### 5. Using Additional Attributes

#### Call Destinations vs Called Countries
```javascript
// Mutually exclusive - can only use one
if (callDestinations.length > 0) {
  calledCountries = []; // Disabled
}
if (calledCountries.length > 0) {
  callDestinations = []; // Disabled
}
```

#### Traffic Segments
```javascript
// Requires home operators to be selected first
if (homeOperators.length === 0) {
  trafficSegments = []; // Disabled
}
```

### 6. Validation Examples

#### Rate Validation
```javascript
// Valid rates: up to 6 integer digits, 10 decimal digits
const validRates = [
  0.05,           // ✅ Valid
  123456.1234567890, // ✅ Valid
  1234567.0,      // ❌ Too many integer digits
  0.12345678901   // ❌ Too many decimal digits
];
```

#### Bounds Validation
```javascript
// Valid bounds: up to 15 integer digits, 5 decimal digits
const validBounds = [
  1000,                    // ✅ Valid
  123456789012345.12345,   // ✅ Valid
  1234567890123456.0,      // ❌ Too many integer digits
  1000.123456              // ❌ Too many decimal digits
];
```

### 7. Error Handling Examples

#### Form Validation Errors
```javascript
// Required field errors
{
  home_operators: "At least 1 home operator is required",
  service_types: "At least 1 service type is required",
  parameters: {
    0: {
      calculation_type: "Calculation type is required"
    }
  }
}
```

#### API Error Responses
```javascript
// Server validation error
{
  "detail": "Invalid operator combination for selected service types"
}

// Multiple validation errors
[
  "Home operators cannot be the same as partner operators",
  "Financial threshold must be greater than lower bound"
]
```

### 8. Common Configuration Patterns

#### Balanced Traffic Model
```json
{
  "model_type": "BALANCED_UNBALANCED_SINGLE_RATE_EFFECTIVE",
  "parameters": [{
    "calculation_type": "SINGLE_RATE_EFFECTIVE",
    "balancing": "BALANCED",
    "basis": "VALUE",
    "basis_value": 0.04
  }]
}
```

#### Tiered Discount Model
```json
{
  "model_type": "STEPPED_TIERED",
  "parameters": [
    {
      "calculation_type": "STEPPED_TIERED",
      "basis": "VALUE",
      "basis_value": 0.05,
      "bound_type": "VOLUME",
      "lower_bound": 0,
      "upper_bound": 10000
    },
    {
      "calculation_type": "STEPPED_TIERED",
      "basis": "VALUE",
      "basis_value": 0.03,
      "bound_type": "VOLUME",
      "lower_bound": 10000,
      "upper_bound": 50000
    }
  ]
}
```

### 9. Best Practices

#### Model Type Selection
- **Single Rate Effective**: Simple, uniform discount rates
- **Stepped/Tiered**: Volume-based progressive discounts
- **Send or Pay**: Traffic direction flexibility
- **IMSI-based**: Subscriber count-based pricing
- **All You Can Eat**: Unlimited usage scenarios

#### Parameter Configuration
- **Always validate bounds**: Lower bound should be less than upper bound
- **Consider business logic**: Ensure discount rates make business sense
- **Test edge cases**: Verify behavior at boundary values
- **Document assumptions**: Clear business rules for each model type

#### Additional Attributes Usage
- **Call Destinations**: Use for specific destination-based discounts
- **Traffic Segments**: Use for granular traffic classification
- **Qualifying Rules**: Use to add eligibility criteria
- **Financial Thresholds**: Use for commitment-based models

#### Sub-Discount Strategy
- **Hierarchical Structure**: Use sub-discounts for exceptions to main rules
- **Rate Inheritance**: Sub-discounts can override parent rates
- **Simplified Models**: Sub-discounts have limited model type options
- **Clear Relationships**: Maintain clear parent-child relationships

### 10. Troubleshooting Common Issues

#### Form Validation Issues
```javascript
// Issue: Form not submitting
// Solution: Check all required fields are filled
const requiredFields = [
  'home_operators', 'partner_operators', 'start_date', 'end_date',
  'direction', 'service_types', 'settlement_method', 'currency_code',
  'tax_type', 'volume_type'
];

// Issue: Parameter validation failing
// Solution: Ensure calculation_type matches model_type requirements
if (modelType === 'SINGLE_RATE_EFFECTIVE') {
  calculationType = 'SINGLE_RATE_EFFECTIVE'; // Must match
}
```

#### Model Type Configuration Issues
```javascript
// Issue: Fields not appearing/disappearing correctly
// Solution: Check model type configuration
const config = discountFormConfigByModelType[modelType.value];
if (!config.availableValues[fieldName]) {
  // Field not available for this model type
}

// Issue: Service types not loading
// Solution: Check service type configuration function
const serviceTypeConfig = config.availableValues.service_types;
const availableServiceTypes = serviceTypeConfig.getAvailableConfig();
```

#### API Integration Issues
```javascript
// Issue: Data transformation errors
// Solution: Verify data format before API call
const transformedData = getConvertedForRequestFormData(formData);
console.log('Transformed data:', transformedData);

// Issue: Validation errors from API
// Solution: Check API response format
catch (error) {
  const errorMessage = error.response.data?.detail ||
                      error.response.data?.[0] ||
                      'Unknown error occurred';
}
```

This comprehensive documentation covers all aspects of the Budget Agreement Discount System, providing developers and users with detailed information about functionality, configuration options, validation rules, API endpoints, and practical usage examples.
